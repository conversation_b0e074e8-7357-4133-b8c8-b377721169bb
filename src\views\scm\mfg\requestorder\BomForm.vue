<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="80%" :loading="formLoading">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
      :inline="true"
    >
      <el-form-item label="需求单号" prop="requestNo">
        <el-input v-model="formData.requestNo" placeholder="保存时自动生成" class="!w-240px" disabled/>
      </el-form-item>
      <el-form-item label="来源类型" prop="orderType">
        <el-select v-model="formData.orderType" placeholder="请选择来源类型" class="!w-240px" disabled>
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.MFG_ORDER_SOURCE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="销售订单" prop="orderDetailId">
        <el-input v-model="formData.orderNo" placeholder="请输入销售订单" class="!w-240px" disabled/>
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-input v-model="formData.productName" placeholder="请输入产品名称" class="!w-240px" disabled/>
      </el-form-item>
      <el-form-item label="订单数量" prop="orderQuantity">
        <el-input
          v-model="formData.orderQuantity"
          placeholder="请输入订单数量"
          class="!w-240px"
          disabled
          @input="handleOrderQuantityChange"
        >
          <template #append>
            <el-select v-model="formData.productUnit" placeholder="请选择订单单位" class="!w-100px" disabled>
            <el-option
              v-for="dict in unitList"
              :key="dict.id"
              :label="dict.name"
              :value="dict.id"
            />
          </el-select>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="订单规格数量" prop="orderSpecQuantity">
        <el-input v-model="formData.orderSpecQuantity" placeholder="请输入订单规格数量" class="!w-240px" disabled />
      </el-form-item>
      <el-form-item label="规格" prop="spec">
        <el-input v-model="formData.spec" placeholder="请输入规格" class="!w-240px" disabled/>
      </el-form-item>
      <el-form-item label="客户" prop="customerName">
        <el-input v-model="formData.customerName" placeholder="请输入客户名称" class="!w-240px" disabled/>
      </el-form-item>
       
      <el-form-item label="下单时间" prop="orderDate">
        <el-date-picker
          v-model="formData.orderDate"
          type="date"
          value-format="x"
          placeholder="选择下单时间" class="!w-240px" disabled
        />
      </el-form-item>
      <el-form-item label="交期" prop="deliverDate">
        <el-date-picker
          v-model="formData.deliverDate"
          type="date"
          value-format="x"
          placeholder="选择交期" class="!w-240px" disabled
        />
      </el-form-item>
      <el-form-item label="Bom编码" prop="bomId">
        <el-select
          v-model="formData.bomId"
          placeholder="请选择Bom编码"
          clearable
          class="!w-240px"
          @change="(val) => {
            // 从bomList中获取选中的bom
            const bom = bomList.find(item => item.id === val)
            formData.bomCode = bom?.code
            formData.bomVersion = bom?.versionNumber || '1.0'
            formData.bomId = val
            formData.slotQuantity = bom?.perMadeQuanlity || bom.quanlity
            console.log('Bom选择变化:',val, bom, formData.bomId, formData.bomCode, formData.bomVersion)
          }"
        >
          <el-option
            v-for="dict in bomList"
            :key="dict.id"
            :label="dict.name ||dict.materialName + ' - ' + dict.code + ' - ' + (dict.versionNumber || '1.0')"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="每槽数量" prop="slotQuantity">
        <el-input
          v-model="formData.slotQuantity"
          placeholder="请输入每槽数量"
          class="!w-240px"
          type="number"
          min="0.1"
          step="0.1"
          @input="handleSlotQuantityChange"
        >
          <template #append>
            <span>{{ unitMap[formData.productUnit] }}</span>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="总槽数" prop="slotCount">
        <el-input v-model="formData.slotCount" placeholder="根据每次数量自动计算" class="!w-240px" type="number" disabled/>
      </el-form-item>
      <el-form-item label="生产要求" prop="requirement">
        <el-input v-model="formData.requirement" placeholder="请输入生产要求" class="!w-610px" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" class="!w-240px" />
      </el-form-item>

    </el-form>
    <!-- 子表的表单 -->
    <el-tabs v-model="subTabsName">
      <el-tab-pane label="生产订单明细" name="requestOrderDetail" v-if="formData.bomId">
        <RequestOrderDetailForm
          ref="requestOrderDetailFormRef"
          :biz-order-id="formData.id"
          :bom-id="formData.bomId"
          :biz-order-no="formData.requestNo"
          :slot-quantity="formData.slotQuantity"
          :order-quantity="formData.orderQuantity"
          :order-spec-quantity="formData.orderSpecQuantity" />
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { RequestOrderApi, RequestOrderVO } from '@/api/scm/mfg/requestorder'
import RequestOrderDetailForm from './components/RequestOrderDetailForm.vue'
import { UnitApi } from '@/api/scm/base/unit'
import { BomApi } from '@/api/scm/mfg/bom'
import { MaterialApi } from '@/api/scm/base/material'
/** 生产订单 表单 */
defineOptions({ name: 'RequestOrderForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  requestNo: undefined,
  customerId: undefined,
  customerName: undefined,
  orderType: 'sale_order', // 默认来源类型为销售订单
  orderId: undefined,
  orderNo: undefined,
  orderDetailId: undefined,
  productId: undefined,
  productCode: undefined,
  productName: undefined,
  productSubType: undefined,
  productUnit: undefined,
  spec: undefined,
  status: undefined,
  approveStatus: undefined,
  approverId: undefined,
  approverName: undefined,
  bomId: undefined,
  bomCode: undefined,
  bomVersion: undefined,
  orderDate: undefined,
  orderQuantity: undefined,
  fulfilledQuantity: undefined,
  orderSpecQuantity: undefined,
  orderUnit: undefined,
  deliverDate: undefined,
  requirement: undefined,
  remark: undefined,
  readyStatus: undefined,
  readyQuantity: undefined,
  slotQuantity: 1,
  slotCount: 0
})
const formRules = reactive({
  bomId: [
    { required: true, message: '请选择Bom编码', trigger: 'blur' },
  ],
  slotQuantity: [
    { required: true, message: '请输入每槽数量', trigger: 'blur' },
  ],
  productName: [
    { required: true, message: '请选择产品', trigger: 'blur' },
  ],
})
const formRef = ref() // 表单 Ref

/** 子表的表单 */
const subTabsName = ref('requestOrderDetail')
const requestOrderDetailFormRef = ref()

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await RequestOrderApi.getRequestOrder(id)
      if(formData.value.productId) {
        await getRemoteBom(formData.value.productId)
      }
      // 数据加载完成后，触发初始计算
      await triggerInitialCalculation()
    } finally {
      formLoading.value = false
    }
  } else {
    // 新建时也需要触发初始计算
    await triggerInitialCalculation()
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 校验子表单
  if(formData.value.bomId) {
    try {
      await requestOrderDetailFormRef.value.validate()
    } catch (e) {
      subTabsName.value = 'requestOrderDetail'
      return
    }
  }
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as RequestOrderVO
    // 拼接子表的数据
    if(formData.value.bomId) {
      data.requestOrderDetails = requestOrderDetailFormRef.value.getData()
    }
    if (formType.value === 'create') {
      await RequestOrderApi.createRequestOrder(data)
      message.success('Bom已确认')
    } else {
      await RequestOrderApi.setBom(data)
      message.success('Bom已确认')
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    requestNo: undefined,
    customerId: undefined,
    customerName: undefined,
    orderType: 'sale_order', // 默认来源类型为销售订单
    orderId: undefined,
    orderNo: undefined,
    orderDetailId: undefined,
    productId: undefined,
    productCode: undefined,
    productName: undefined,
    productSubType: undefined,
    productUnit: undefined,
    spec: undefined,
    status: undefined,
    approveStatus: undefined,
    approverId: undefined,
    approverName: undefined,
    bomId: undefined,
    bomCode: undefined,
    bomVersion: undefined,
    orderDate: undefined,
    orderQuantity: undefined,
    fulfilledQuantity: undefined,
    orderSpecQuantity: undefined,
    orderUnit: undefined,
    deliverDate: undefined,
    requirement: undefined,
    remark: undefined,
    readyStatus: undefined,
    readyQuantity: undefined,
    slotQuantity: 1,
    slotCount: 0
  }
  formRef.value?.resetFields()
}

const orderDetailDefault = computed(() => {
  if (formData.value.orderDetailId === undefined) return null
  return {
    id: formData.value.orderDetailId,
    label: [
      formData.value.orderNo, 
      formData.value.productName,
      formData.value.productCode
    ].filter(Boolean).join(' - ')
  }
})

// 从单位接口获取单位列表
const unitList = ref([])
const unitMap = ref({})
const getRemoteUnit = async () => {
  const data = await UnitApi.getUnitPage({ pageSize: 100, pageNo: 1 });
  unitList.value = data.list
  console.log('unitList', unitList.value)
  unitList.value.forEach(unit => {
    unitMap.value[unit.id] = unit.name
  })
}

// 获取bom列表
const bomList = ref([])
const getRemoteBom = async (materialId) => {
  if(!materialId) {
    bomList.value = []
    return
  }
  const data = await BomApi.getSimpleBomPage({ pageSize: 20, pageNo: 1, materialId: materialId });
  bomList.value = data.list
  if(bomList.value.length > 0 && formData.value.bomId) {
    // 如果没有选择Bom编码，默认选择第一个
    formData.value.bomId = bomList.value[0].id
    formData.value.bomCode = bomList.value[0].code
    formData.value.bomVersion = bomList.value[0].versionNumber || '1.0'
    formData.value.slotQuantity = bomList.value[0].quantity || 1
  }
  console.log('bomList', bomList.value)
}

const getRemoteMaterial = async (params) => {
  const { pageNo, pageSize, query, ...restParams } = params;
  const response = await MaterialApi.getSimpleMaterialPage({
    pageNo,
    pageSize,
    name: query, // 映射到 API 的 `name` 字段
    ...restParams,
  });
  const { list, total } = await response;
  return { list, total };
}

onMounted(async () => {
  await getRemoteUnit() // 获取单位列表
})

/** 计算总槽数 */
const calculateSlotCount = () => {
  const { orderQuantity, slotQuantity } = formData.value
  if (!orderQuantity || !slotQuantity || slotQuantity <= 0) {
    formData.value.slotCount = 0
    return
  }
  // 向上取整
  formData.value.slotCount = Math.ceil(orderQuantity / slotQuantity)
}

/** 处理每槽数量变化 */
const handleSlotQuantityChange = () => {
  // 重新计算总槽数
  calculateSlotCount()
  // 通知子表重新计算
  nextTick(() => {
    if (requestOrderDetailFormRef.value && requestOrderDetailFormRef.value.recalculateAll) {
      requestOrderDetailFormRef.value.recalculateAll()
    }
  })
}

/** 处理订单数量变化 */
const handleOrderQuantityChange = () => {
  calculateSlotCount()
  // 通知子表重新计算
  nextTick(() => {
    if (requestOrderDetailFormRef.value && requestOrderDetailFormRef.value.recalculateAll) {
      requestOrderDetailFormRef.value.recalculateAll()
    }
  })
}

/** 处理BOM变化时的计算 */
const handleBomChange = (bomData: any) => {
  // 设置每槽数量
  formData.value.slotQuantity = bomData.perMadeQuanlity || bomData.quanlity || 1
  // 重新计算总槽数
  calculateSlotCount()
  // 通知子表重新计算
  nextTick(() => {
    if (requestOrderDetailFormRef.value && requestOrderDetailFormRef.value.recalculateAll) {
      requestOrderDetailFormRef.value.recalculateAll()
    }
  })
}

/** 触发初始计算 */
const triggerInitialCalculation = async () => {
  // 等待DOM更新
  await nextTick()

  // 计算总槽数
  calculateSlotCount()

  // 等待子表组件准备就绪，然后触发重新计算
  setTimeout(() => {
    if (requestOrderDetailFormRef.value && requestOrderDetailFormRef.value.recalculateAll) {
      console.log('触发初始计算')
      requestOrderDetailFormRef.value.recalculateAll()
    }
  }, 100) // 给子表组件一点时间来初始化
}

//如果计划数量改变，或者每槽数量改变，计算总槽数
// 改为深度监听单独字段，更可靠
watch(
  () => ({
    q: formData.value.orderQuantity, 
    s: formData.value.slotQuantity
  }),
  () => {
    console.log('检测到数量变化') // 调试用
    calculateSlotCount()
  },
  { immediate: true, deep: true }
)
</script>
